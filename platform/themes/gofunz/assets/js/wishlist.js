class WishlistManager {
    constructor() {
        this.init();
    }

    init() {
        document.addEventListener("click", (e) => {
            if (e.target.matches(".wishlist-toggle, .wishlist-toggle *")) {
                e.preventDefault();
                const button = e.target.closest(".wishlist-toggle");
                this.toggleWishlist(button);
            }
        });
    }

    async toggleWishlist(button) {
        const placeId = button.dataset.placeId;
        const isAuthenticated = button.dataset.authenticated === "true";

        if (!isAuthenticated) {
            // Redirect to login or show auth modal
            const authModal = document.querySelector(
                '[data-bs-toggle="modal"][data-bs-target="#signup"]',
            );
            if (authModal) {
                authModal.click();
            }
            return;
        }

        try {
            button.disabled = true;

            // Get CSRF token
            const csrfToken = document.querySelector(
                'meta[name="csrf-token"]',
            )?.content;

            const response = await fetch(`/api/booking/wishlist/${placeId}`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                    Accept: "application/json",
                },
            });

            const data = await response.json();

            if (response.ok) {
                this.updateButtonState(button, data.is_wishlisted);
                this.showNotification(data.message);
            } else {
                this.showError(data.message || "An error occurred");
            }
        } catch (error) {
            console.error("Wishlist error:", error);
            this.showError("Network error occurred");
        } finally {
            button.disabled = false;
        }
    }

    updateButtonState(button, isWishlisted) {
        const icon = button.querySelector("i");
        if (isWishlisted) {
            icon.className = "fas fa-heart text-red-500";
            button.title = "Remove from wishlist";
        } else {
            icon.className = "far fa-heart text-gray-600";
            button.title = "Add to wishlist";
        }
    }

    showNotification(message) {
        // Create a simple toast notification
        const toast = document.createElement("div");
        toast.className =
            "fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 transition-opacity";
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = "0";
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }

    showError(message) {
        // Create a simple error toast notification
        const toast = document.createElement("div");
        toast.className =
            "fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg z-50 transition-opacity";
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = "0";
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }
}

// Initialize wishlist manager when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
    new WishlistManager();
});
